import { showInformations } from "@/api/user.js";
import { isLogin } from "@/utils/auth.js";

export default {
  namespaced: true,
  state: {
    userInfo: {
      adminRabk: "0", // 代理等级: "0"=普通用户, "1"=1级代理, "2"=2级代理, "3"=总代理
      headPic: "",
      phone: "",
      userName: "",
      integral: 0,
      carId: "", // 车牌号
    },
  },
  getters: {
    userInfo: state => state.userInfo,
  },
  mutations: {
    SETUSERINFO(state, data) {
      state.userInfo = data
        ? { ...data }
        : {
            adminRabk: "0",
            headPic: "",
            phone: "",
            userName: "",
            integral: 0,
            carId: "", // 车牌号
          };
    },
    DEFAULTUSERINFO(state) {
      state.userInfo = {
        adminRabk: "0",
        headPic: "",
        phone: "",
        userName: "",
        integral: 0,
        carId: "", // 车牌号
      };
    },
  },
  actions: {
    async getUserInfo({ commit }) {
      if (!isLogin()) {
        return Promise.reject(new Error("用户未登录"));
      }

      try {
        const res = await showInformations();
        console.log("[User] 获取用户信息成功:", res);
        if (res.data.code !== 200) {
          return Promise.reject(
            new Error(res.data.message || "获取用户信息失败")
          );
        }
        commit("SETUSERINFO", res.data.data);
        return res.data.data;
      } catch (error) {
        console.error("[User] 获取用户信息失败:", error);
        return Promise.reject(error);
      }
    },
  },
};
